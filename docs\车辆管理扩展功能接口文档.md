# 车辆管理扩展功能接口文档

## 功能概述

车辆管理新增以下字段，支持员工端定期更新，后台显示最终提交时间和提交人：

- 里程数（公里）
- 外观描述
- 保险到期时间
- 行驶证到期时间
- 物资清单
- 最后提交时间
- 最后提交人

## 数据库变更

### 新增字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| mileage | DECIMAL(10,2) | 里程数（公里） |
| appearance | TEXT | 外观描述 |
| insuranceExpiry | DATETIME | 保险到期时间 |
| licenseExpiry | DATETIME | 行驶证到期时间 |
| supplies | TEXT | 物资清单 |
| lastSubmittedAt | DATETIME | 最后更新时间（员工端提交） |
| lastSubmittedBy | INT | 最后提交人ID |

## API 接口

### 员工端接口

#### 1. 获取员工关联的车辆信息

```
GET /openapi/vehicles/employee/{employeeId}
```

**参数说明：**
- employeeId: 员工ID

**响应示例：**
```json
{
  "id": 1,
  "plateNumber": "京A12345",
  "vehicleType": "小型车",
  "status": "空闲",
  "mileage": 15000.50,
  "appearance": "车身整洁，无明显划痕",
  "insuranceExpiry": "2024-12-31T00:00:00.000Z",
  "licenseExpiry": "2025-06-30T00:00:00.000Z",
  "supplies": "洗车工具、毛巾、清洁剂",
  "lastSubmittedAt": "2025-07-02T10:30:00.000Z",
  "lastSubmittedBy": 5,
  "lastSubmittedEmployee": {
    "id": 5,
    "name": "张三",
    "phone": "13800138000"
  },
  "employee": {
    "id": 5,
    "name": "张三",
    "phone": "13800138000"
  }
}
```

#### 2. 更新车辆信息

```
PUT /openapi/vehicles/{vehicleId}/info?employeeId={employeeId}
```

**参数说明：**
- vehicleId: 车辆ID
- employeeId: 员工ID（查询参数）

**请求体：**
```json
{
  "mileage": 15100.50,
  "appearance": "车身整洁，右侧有轻微划痕",
  "insuranceExpiry": "2024-12-31",
  "licenseExpiry": "2025-06-30",
  "supplies": "洗车工具、毛巾、清洁剂、新增吸尘器"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "车辆信息更新成功"
}
```

#### 3. 获取车辆详细信息

```
GET /openapi/vehicles/{vehicleId}
```

**参数说明：**
- vehicleId: 车辆ID

**响应示例：**
```json
{
  "id": 1,
  "plateNumber": "京A12345",
  "vehicleType": "小型车",
  "status": "空闲",
  "mileage": 15100.50,
  "appearance": "车身整洁，右侧有轻微划痕",
  "insuranceExpiry": "2024-12-31T00:00:00.000Z",
  "licenseExpiry": "2025-06-30T00:00:00.000Z",
  "supplies": "洗车工具、毛巾、清洁剂、新增吸尘器",
  "lastSubmittedAt": "2025-07-02T14:30:00.000Z",
  "lastSubmittedBy": 5,
  "lastSubmittedEmployee": {
    "id": 5,
    "name": "张三",
    "phone": "13800138000"
  }
}
```

### 管理端接口

#### 1. 查询车辆详情（已更新）

```
GET /vehicles/{id}
```

**说明：** 原有接口已更新，现在返回包含提交记录的详细信息。

**响应示例：**
```json
{
  "id": 1,
  "plateNumber": "京A12345",
  "vehicleType": "小型车",
  "latitude": 39.9042,
  "longitude": 116.4074,
  "status": "空闲",
  "mileage": 15100.50,
  "appearance": "车身整洁，右侧有轻微划痕",
  "insuranceExpiry": "2024-12-31T00:00:00.000Z",
  "licenseExpiry": "2025-06-30T00:00:00.000Z",
  "supplies": "洗车工具、毛巾、清洁剂、新增吸尘器",
  "lastSubmittedAt": "2025-07-02T14:30:00.000Z",
  "lastSubmittedBy": 5,
  "lastSubmittedEmployee": {
    "id": 5,
    "name": "张三",
    "phone": "13800138000"
  },
  "employee": {
    "id": 5,
    "name": "张三",
    "phone": "13800138000",
    "position": "洗护师"
  },
  "createdAt": "2025-01-01T00:00:00.000Z",
  "updatedAt": "2025-07-02T14:30:00.000Z"
}
```

## 数据验证规则

### VehicleUpdateDto

| 字段 | 验证规则 |
|------|----------|
| mileage | 可选，数字类型，最小值0，最大值999999 |
| appearance | 可选，字符串类型，最大长度1000字符 |
| insuranceExpiry | 可选，日期类型 |
| licenseExpiry | 可选，日期类型 |
| supplies | 可选，字符串类型，最大长度2000字符 |

## 业务规则

1. **权限控制**：目前采用宽松策略，允许所有员工更新任何车辆信息
2. **自动记录**：每次更新时自动记录提交时间和提交人
3. **历史追踪**：管理端可以查看最后一次提交的时间和提交人信息
4. **数据完整性**：所有新增字段均为可选，不影响现有数据

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 员工ID不能为空 |
| 404 | 员工不存在 |
| 404 | 车辆不存在 |
| 404 | 该员工未分配车辆 |
| 404 | 车辆信息不存在 |

## 使用场景

1. **员工端定期更新**：员工可以定期更新车辆的里程数、外观状况、物资清单等信息
2. **管理端监控**：管理员可以查看车辆信息的最后更新时间和更新人，便于管理和监督
3. **到期提醒**：可以基于保险和行驶证到期时间设置提醒功能（后续扩展）
4. **维护记录**：外观描述和物资清单可以作为车辆维护的参考记录
