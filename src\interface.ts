/* eslint-disable @typescript-eslint/no-unused-vars */
// 定义 token payload 接口
interface TokenPayload {
  userId: number;
  username?: string;
  roles?: string[];
  type: 'access' | 'refresh';
  exp?: number;
  iat?: number;
}

/** 小程序用户注册数据 */
interface RegisterUserInfo {
  /** 微信小程序用户openid */
  openid: string;
  /** 用户头像 */
  avatarUrl: string;
  /** 昵称 */
  nickName: string;
  /** 手机号 */
  phone: string;
  /** 性别 */
  gender: number;
}

interface Address_Weapp {
  /** 客户ID */
  customerId: string;
  /** 联系人姓名 */
  contactName: string;
  /** 联系电话 */
  contactPhone: string;
  /** 详细地址 */
  addressText: string;
  /** 地区文本，格式：省,市,区 */
  addressArea: string;
  /** 地区编码，6位数 */
  addressCode: string;
  /** 经度 */
  longitude: string;
  /** 纬度 */
  latitude: string;
  /** 备注 */
  remark?: string;
  /** 是否默认地址 1-是 0-否 */
  isDefault: string;
}

/** 订单地址更新接口 */
interface UpdateOrderAddress {
  /** 服务地址 */
  address?: string;
  /** 服务地址详情 */
  addressDetail?: string;
  /** 服务地址经度 */
  longitude?: number;
  /** 服务地址纬度 */
  latitude?: number;
  /** 服务地址备注 */
  addressRemark?: string;
  /** 地址ID（可选，如果提供则关联到客户地址，null表示取消关联） */
  addressId?: number | null;
}

/** 创建订单明细 */
interface CreateOrderDetail {
  /** 服务ID */
  serviceId: number;
  /** 服务名称，确保删除服务后订单明细的服务名称不丢失 */
  serviceName: string;
  /** 服务基础价格，确保服务价格变更后订单明细的价格不受影响 */
  servicePrice: number;
  /** 宠物ID */
  petId: number;
  /** 宠物名称，确保删除宠物后订单明细的宠物名称不丢失 */
  petName: string;
  /** 宠物类型，确保删除宠物后订单明细的宠物类型不丢失 */
  petType: string;
  /** 宠物品种，确保删除宠物后订单明细的宠物品种不丢失 */
  petBreed: string;
  /** 下单时间 */
  orderTime: Date;
  /** 用户备注 */
  userRemark?: string;
  /** 增项服务ID列表 */
  addServiceIds: number[][];
}

enum DiscountType {
  /** 代金券 */
  COUPON = 'coupon',
  /** 权益卡 */
  MEMBERSHIP_CARD = 'membership_card',
}

/** 创建订单数据 */
interface CreateOrderData {
  /** 客户ID */
  customerId: number;
  /** 员工ID */
  employeeId?: number;
  /** 下单时间 */
  orderTime: Date;
  /** 预约服务时间 */
  serviceTime: Date | null;
  /** 订单原价 */
  originalPrice: number;
  /** 订单总费用 */
  totalFee: number;
  /** 权益卡抵扣金额 */
  cardDeduction: number;
  /** 代金券抵扣金额 */
  couponDeduction: number;
  /** 订单明细列表 */
  orderDetails: CreateOrderDetail[];
  /** 优惠信息列表 */
  discountInfos?: Array<{
    discountType: DiscountType;
    discountId: number;
    discountAmount: number;
  }>;
}

/**
 * 微信支付JSAPI下单接口参数
 */
interface WepayJsapiParams {
  /**
   * 服务商APPID
   * 服务商在微信开放平台或公众平台上申请的唯一标识
   * 需与sp_mchid有绑定关系
   */
  sp_appid: string;

  /**
   * 服务商商户号
   * 微信支付系统生成并分配的唯一标识符
   */
  sp_mchid: string;

  /**
   * 子商户APPID
   * 子商户在微信开放平台或公众平台上申请的唯一标识
   * 需与sub_mchid有绑定关系
   */
  sub_appid?: string;

  /**
   * 子商户号
   * 由服务商为子商户进件后获取
   */
  sub_mchid: string;

  /**
   * 商品描述
   * 用户微信账单可见的商品信息描述
   * 最大长度127字符
   */
  description: string;

  /**
   * 商户订单号
   * 服务商系统内部订单号
   * 6-32个字符，只能包含数字、大小写字母_-|*
   * 同一服务商商户号下唯一
   */
  out_trade_no: string;

  /**
   * 支付结束时间
   * 遵循rfc3339标准格式：yyyy-MM-DDTHH:mm:ss+TIMEZONE
   * 例如：2015-05-20T13:29:35+08:00
   */
  time_expire?: string;

  /**
   * 商户数据包
   * 服务商在创建订单时可传入自定义数据包，该数据对用户不可见
   * 用于存储订单相关的服务商自定义信息，总长度限制在128字符以内
   */
  attach?: string;

  /**
   * 商户回调地址
   * 服务商接收支付成功回调通知的地址
   */
  notify_url: string;

  /**
   * 订单优惠标记
   * 代金券批次配置的优惠标记，用于指定订单参与的优惠活动
   */
  goods_tag?: string;

  /**
   * 结算信息
   */
  settle_info?: {
    /**
     * 电子发票入口开放标识
     * true: 开启；false: 关闭
     */
    support_fapiao?: boolean;
  };

  /**
   * 订单金额信息
   */
  amount: {
    /**
     * 订单总金额，单位为分
     */
    total: number;

    /**
     * 货币类型
     * 符合ISO 4217标准的三位字母代码，固定值：CNY
     */
    currency?: string;
  };

  /**
   * 支付者信息
   */
  payer: {
    /**
     * 用户服务商标识
     * 用户在服务商sp_appid下的唯一标识
     */
    sp_openid?: string;

    /**
     * 用户子商户标识
     * 用户在子商户sub_appid下的唯一标识
     */
    sub_openid?: string;
  };
}
