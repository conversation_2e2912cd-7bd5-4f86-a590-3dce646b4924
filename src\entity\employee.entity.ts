import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  BeforeCreate,
} from 'sequelize-typescript';
import { Vehicle } from './vehicle.entity';
import { ServiceDurationRecord } from './service-duration-record.entity';
import { Order } from './order.entity';
import { generateUniqueCode } from '../common/Utils';

export interface EmployeeAttributes {
  /** 员工ID */
  id: number;
  /** 微信openid */
  openid?: string;
  /** 真实姓名 */
  name: string;
  /** 手机号 */
  phone: string;
  /** 头像 */
  avatar?: string;
  /** 职位 */
  position?: string;
  /** 接单等级（1-5级） */
  level?: number;
  /** 工作经验（月） */
  workExp?: number;
  /** 服务评分（0-5分） */
  rating?: number;
  /** 钱包余额 */
  walletBalance: number;
  /** 所属车辆ID */
  vehicleId?: number;
  /** 推广码 */
  promotionCode: string;
  /** 关联的车辆信息 */
  vehicle?: Vehicle;
  /** 关联的订单列表 */
  orders?: Order[];
  /** 状态：0-禁用 1-启用 */
  status: number;
}

@Table({ tableName: 'employees', timestamps: true, comment: '员工表' })
export class Employee
  extends Model<EmployeeAttributes>
  implements EmployeeAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '员工ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    comment: '微信openid',
  })
  openid: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '真实姓名',
  })
  name: string;

  @Column({
    type: DataType.STRING(20),
    unique: {
      name: 'phone',
      msg: '已存在相同的手机号',
    },
    allowNull: false,
    comment: '手机号',
  })
  phone: string;

  @Column({
    type: DataType.STRING(255),
    comment: '头像',
  })
  avatar: string;

  @Column({
    type: DataType.STRING(50),
    comment: '职位',
  })
  position: string;

  @Column({
    type: DataType.INTEGER,
    comment: '接单等级（1-5级）',
  })
  level: number;

  @Column({
    type: DataType.INTEGER,
    comment: '工作经验（月）',
  })
  workExp: number;

  @Column({
    type: DataType.FLOAT,
    comment: '服务评分（0-5分）',
  })
  rating: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    defaultValue: 0.0,
    comment: '钱包余额',
  })
  walletBalance: number;

  @Column({
    type: DataType.INTEGER,
    comment: '所属车辆ID',
  })
  @ForeignKey(() => Vehicle)
  vehicleId: number;

  @Column({
    type: DataType.STRING(20),
    unique: {
      name: 'employee_promotion_code',
      msg: '员工推广码已存在',
    },
    allowNull: false,
    comment: '推广码',
  })
  promotionCode: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '状态：0-禁用 1-启用',
  })
  status: number;

  @BelongsTo(() => Vehicle, {
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  })
  vehicle: Vehicle;

  @HasMany(() => Order)
  orders: Order[];

  @HasMany(() => ServiceDurationRecord)
  serviceDurationRecords: ServiceDurationRecord[];

  @BeforeCreate
  static async generateUniqueCode(instance: Employee) {
    if (!instance.promotionCode) {
      instance.promotionCode = generateUniqueCode();
    }
  }
}
