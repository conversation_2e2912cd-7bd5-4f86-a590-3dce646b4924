-- 车辆管理字段扩展 SQL 脚本
-- 执行时间：2025-07-02

-- 为车辆表添加新字段
ALTER TABLE vehicles 
ADD COLUMN mileage DECIMAL(10,2) COMMENT '里程数（公里）',
ADD COLUMN appearance TEXT COMMENT '外观描述',
ADD COLUMN insuranceExpiry DATETIME COMMENT '保险到期时间',
ADD COLUMN licenseExpiry DATETIME COMMENT '行驶证到期时间',
ADD COLUMN supplies TEXT COMMENT '物资清单',
ADD COLUMN lastSubmittedAt DATETIME COMMENT '最后更新时间（员工端提交）',
ADD COLUMN lastSubmittedBy INT COMMENT '最后提交人ID';

-- 添加索引以提高查询性能
CREATE INDEX idx_vehicles_last_submitted_by ON vehicles(lastSubmittedBy);
CREATE INDEX idx_vehicles_insurance_expiry ON vehicles(insuranceExpiry);
CREATE INDEX idx_vehicles_license_expiry ON vehicles(licenseExpiry);

-- 添加外键约束（可选，根据实际需求决定是否添加）
-- ALTER TABLE vehicles 
-- ADD CONSTRAINT fk_vehicles_last_submitted_by 
-- FOREIGN KEY (lastSubmittedBy) REFERENCES employees(id) ON DELETE SET NULL ON UPDATE CASCADE;
