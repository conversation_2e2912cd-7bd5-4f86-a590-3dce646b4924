# 车辆管理功能测试说明

## 测试前准备

### 1. 数据库更新
执行以下 SQL 脚本添加新字段：

```sql
-- 为车辆表添加新字段
ALTER TABLE vehicles 
ADD COLUMN mileage DECIMAL(10,2) COMMENT '里程数（公里）',
ADD COLUMN appearance TEXT COMMENT '外观描述',
ADD COLUMN insuranceExpiry DATETIME COMMENT '保险到期时间',
ADD COLUMN licenseExpiry DATETIME COMMENT '行驶证到期时间',
ADD COLUMN supplies TEXT COMMENT '物资清单',
ADD COLUMN lastSubmittedAt DATETIME COMMENT '最后更新时间（员工端提交）',
ADD COLUMN lastSubmittedBy INT COMMENT '最后提交人ID';
```

### 2. 重启服务
```bash
npm run dev
```

## 测试用例

### 测试用例 1：员工获取关联车辆信息

**接口：** `GET /openapi/vehicles/employee/{employeeId}`

**测试步骤：**
1. 确保数据库中有员工记录，且该员工已分配车辆
2. 发送请求获取员工关联的车辆信息
3. 验证返回的车辆信息包含新增字段

**预期结果：**
- 返回车辆详细信息
- 包含 mileage、appearance、insuranceExpiry、licenseExpiry、supplies 等新字段
- 如果有提交记录，显示 lastSubmittedAt、lastSubmittedBy、lastSubmittedEmployee

### 测试用例 2：员工更新车辆信息

**接口：** `PUT /openapi/vehicles/{vehicleId}/info?employeeId={employeeId}`

**测试数据：**
```json
{
  "mileage": 15000.50,
  "appearance": "车身整洁，无明显划痕",
  "insuranceExpiry": "2024-12-31",
  "licenseExpiry": "2025-06-30",
  "supplies": "洗车工具、毛巾、清洁剂"
}
```

**测试步骤：**
1. 发送 PUT 请求更新车辆信息
2. 验证返回成功响应
3. 查询车辆信息确认更新成功
4. 验证 lastSubmittedAt 和 lastSubmittedBy 字段自动更新

**预期结果：**
- 返回 `{"success": true, "message": "车辆信息更新成功"}`
- 车辆信息成功更新
- lastSubmittedAt 更新为当前时间
- lastSubmittedBy 更新为提交的员工ID

### 测试用例 3：管理端查看车辆详情

**接口：** `GET /vehicles/{id}`

**测试步骤：**
1. 发送请求查询车辆详情
2. 验证返回信息包含提交记录

**预期结果：**
- 返回完整的车辆信息
- 包含 lastSubmittedEmployee 信息（提交人的姓名和电话）
- 显示最后提交时间

### 测试用例 4：数据验证测试

**接口：** `PUT /openapi/vehicles/{vehicleId}/info?employeeId={employeeId}`

**测试数据（无效数据）：**
```json
{
  "mileage": -100,
  "appearance": "这是一个超过1000字符的描述..." // 超长字符串
}
```

**预期结果：**
- 返回验证错误信息
- mileage 不能为负数
- appearance 不能超过1000字符

### 测试用例 5：权限验证测试

**测试步骤：**
1. 使用不存在的员工ID更新车辆信息
2. 使用不存在的车辆ID更新信息
3. 员工未分配车辆时获取车辆信息

**预期结果：**
- 员工不存在时返回404错误
- 车辆不存在时返回404错误
- 员工未分配车辆时返回400错误

## 手动测试示例

### 使用 curl 测试

1. **获取员工车辆信息：**
```bash
curl -X GET "http://localhost:7001/openapi/vehicles/employee/1"
```

2. **更新车辆信息：**
```bash
curl -X PUT "http://localhost:7001/openapi/vehicles/1/info?employeeId=1" \
  -H "Content-Type: application/json" \
  -d '{
    "mileage": 15000.50,
    "appearance": "车身整洁，无明显划痕",
    "insuranceExpiry": "2024-12-31",
    "licenseExpiry": "2025-06-30",
    "supplies": "洗车工具、毛巾、清洁剂"
  }'
```

3. **查看车辆详情：**
```bash
curl -X GET "http://localhost:7001/vehicles/1"
```

## 注意事项

1. **数据库字段**：确保数据库已添加新字段
2. **时区处理**：注意日期时间的时区转换
3. **数据类型**：mileage 使用 DECIMAL 类型，注意精度
4. **权限控制**：当前采用宽松策略，后续可根据需求调整
5. **错误处理**：测试各种异常情况的错误处理

## 后续扩展建议

1. **到期提醒**：基于保险和行驶证到期时间设置提醒功能
2. **历史记录**：记录车辆信息的修改历史
3. **权限细化**：根据业务需求细化权限控制
4. **数据统计**：添加车辆使用情况的统计功能
